%-------------------------------------------------------------------------%
%--------          ANM-DCFT vs Original DCFT Comparison Test          -%
%-------------------------------------------------------------------------%

clc; clear all; close all;

fprintf('========== ANM-DCFT vs DCFT 算法对比测试 ==========\n\n');

% 运行增强的ANM-DCFT算法
fprintf('正在运行增强的ANM-DCFT算法...\n');
Enhanced_ANM_DCFT_ISAR();

% 等待用户查看结果
fprintf('\n请查看成像结果对比图...\n');
fprintf('按任意键继续进行详细性能分析...\n');
pause;

%% 详细性能分析
fprintf('\n========== 详细性能分析 ==========\n');

% 重新加载数据进行更详细的分析
[s_r_tm2, tm2, r, PRF, fc, B, c, Pos] = load_ship_data_test();
target_range = 30:56;

% ANM-DCFT参数优化测试
fprintf('\n正在测试不同参数组合的性能...\n');
parameter_sensitivity_analysis(s_r_tm2, tm2, target_range);

%% 参数敏感性分析
function parameter_sensitivity_analysis(s_r_tm2, tm2, target_range)
    
    % 测试不同的lambda值
    lambda_values = [0.01, 0.05, 0.1, 0.2, 0.5];
    mu_values = [0.001, 0.01, 0.05, 0.1];
    
    results = struct();
    
    fprintf('测试不同稀疏正则化参数 λ...\n');
    for i = 1:length(lambda_values)
        fprintf('  λ = %.3f\n', lambda_values(i));
        
        params = struct();
        params.lambda = lambda_values(i);
        params.mu = 0.01;
        params.max_iter = 10;  % 减少迭代次数以加速测试
        params.tol = 1e-6;
        params.L_windows = 8;
        params.F_max = 700;
        
        tic;
        [R_D_ANM, alpha_est, beta_est, convergence_info] = ANM_DCFT_imaging_test(s_r_tm2, tm2, target_range, params);
        time_elapsed = toc;
        
        % 计算性能指标
        entropy_val = EntropyImage_test(abs(R_D_ANM) + eps);
        contrast_val = contrast_test(abs(R_D_ANM) + eps);
        
        results.lambda(i) = struct('value', lambda_values(i), 'entropy', entropy_val, ...
                                  'contrast', contrast_val, 'time', time_elapsed);
    end
    
    fprintf('\n测试不同平滑正则化参数 μ...\n');
    for i = 1:length(mu_values)
        fprintf('  μ = %.3f\n', mu_values(i));
        
        params = struct();
        params.lambda = 0.1;  % 使用最优lambda
        params.mu = mu_values(i);
        params.max_iter = 10;
        params.tol = 1e-6;
        params.L_windows = 8;
        params.F_max = 700;
        
        tic;
        [R_D_ANM, alpha_est, beta_est, convergence_info] = ANM_DCFT_imaging_test(s_r_tm2, tm2, target_range, params);
        time_elapsed = toc;
        
        entropy_val = EntropyImage_test(abs(R_D_ANM) + eps);
        contrast_val = contrast_test(abs(R_D_ANM) + eps);
        
        results.mu(i) = struct('value', mu_values(i), 'entropy', entropy_val, ...
                              'contrast', contrast_val, 'time', time_elapsed);
    end
    
    % 显示参数分析结果
    display_parameter_analysis(results, lambda_values, mu_values);
    
end

%% 显示参数分析结果
function display_parameter_analysis(results, lambda_values, mu_values)
    
    figure('Position', [200, 200, 1000, 600]);
    
    % Lambda参数分析
    subplot(2,2,1);
    lambda_entropy = [results.lambda.entropy];
    plot(lambda_values, lambda_entropy, 'b-o', 'LineWidth', 2);
    xlabel('λ (稀疏正则化参数)');
    ylabel('图像熵');
    title('稀疏参数对图像熵的影响');
    grid on;
    
    subplot(2,2,2);
    lambda_contrast = [results.lambda.contrast];
    plot(lambda_values, lambda_contrast, 'r-s', 'LineWidth', 2);
    xlabel('λ (稀疏正则化参数)');
    ylabel('图像对比度');
    title('稀疏参数对图像对比度的影响');
    grid on;
    
    % Mu参数分析
    subplot(2,2,3);
    mu_entropy = [results.mu.entropy];
    plot(mu_values, mu_entropy, 'g-^', 'LineWidth', 2);
    xlabel('μ (平滑正则化参数)');
    ylabel('图像熵');
    title('平滑参数对图像熵的影响');
    grid on;
    
    subplot(2,2,4);
    mu_contrast = [results.mu.contrast];
    plot(mu_values, mu_contrast, 'm-d', 'LineWidth', 2);
    xlabel('μ (平滑正则化参数)');
    ylabel('图像对比度');
    title('平滑参数对图像对比度的影响');
    grid on;
    
    % 打印最优参数
    [~, best_lambda_idx] = min(lambda_entropy);
    [~, best_mu_idx] = min(mu_entropy);
    
    fprintf('\n========== 参数优化建议 ==========\n');
    fprintf('最优稀疏正则化参数 λ: %.3f (图像熵: %.4f)\n', ...
            lambda_values(best_lambda_idx), lambda_entropy(best_lambda_idx));
    fprintf('最优平滑正则化参数 μ: %.3f (图像熵: %.4f)\n', ...
            mu_values(best_mu_idx), mu_entropy(best_mu_idx));
    
    % 计算处理时间对比
    lambda_times = [results.lambda.time];
    mu_times = [results.mu.time];
    
    fprintf('\n========== 计算时间分析 ==========\n');
    fprintf('平均处理时间 (不同λ): %.2f ± %.2f 秒\n', mean(lambda_times), std(lambda_times));
    fprintf('平均处理时间 (不同μ): %.2f ± %.2f 秒\n', mean(mu_times), std(mu_times));
    
end

%% 简化的ANM-DCFT成像函数 (用于测试)
function [R_D_ANM, alpha_est, beta_est, convergence_info] = ANM_DCFT_imaging_test(s_r_tm, tm, target_range, params)
    
    [Num_r, Num_tm] = size(s_r_tm);
    R = length(target_range);
    
    % 初始化
    alpha_est = zeros(R, 1);
    beta_est = zeros(R, 1);
    x_r = cell(R, 1);
    
    % 简化的初始化
    for idx = 1:R
        r_idx = target_range(idx);
        x_r{idx} = fft(s_r_tm(r_idx, :), params.L_windows * Num_tm);
        alpha_est(idx) = 40;  % 默认初始值
        beta_est(idx) = 400;
    end
    
    % 收敛信息
    convergence_info = struct();
    convergence_info.cost = zeros(params.max_iter, 1);
    convergence_info.alpha_change = zeros(params.max_iter, 1);
    convergence_info.beta_change = zeros(params.max_iter, 1);
    
    % 简化的迭代优化
    for iter = 1:params.max_iter
        alpha_prev = alpha_est;
        beta_prev = beta_est;
        
        % 频谱优化 (简化版本)
        for idx = 1:R
            r_idx = target_range(idx);
            s_obs = s_r_tm(r_idx, :).';
            
            % 简单的相位补偿
            phase_comp = exp(-1j * 2*pi * (0.5*alpha_est(idx)*tm.^2 + (1/6)*beta_est(idx)*tm.^3));
            s_comp = s_obs.' .* phase_comp;
            x_r{idx} = fft(s_comp, params.L_windows * Num_tm);
            
            % 软阈值处理
            threshold = params.lambda * max(abs(x_r{idx}));
            x_r{idx} = x_r{idx} .* (abs(x_r{idx}) > threshold);
        end
        
        % 相位参数更新 (简化版本)
        for idx = 1:R
            if idx > 1 && idx < R
                % 平滑约束
                alpha_est(idx) = 0.7*alpha_est(idx) + 0.15*alpha_est(idx-1) + 0.15*alpha_est(idx+1);
                beta_est(idx) = 0.7*beta_est(idx) + 0.15*beta_est(idx-1) + 0.15*beta_est(idx+1);
            end
        end
        
        % 记录收敛信息
        convergence_info.alpha_change(iter) = norm(alpha_est - alpha_prev);
        convergence_info.beta_change(iter) = norm(beta_est - beta_prev);
        convergence_info.cost(iter) = sum([convergence_info.alpha_change(iter), convergence_info.beta_change(iter)]);
        
        % 检查收敛
        if iter > 1 && convergence_info.cost(iter) < params.tol
            break;
        end
    end
    
    % 构建成像结果
    R_D_ANM = zeros(Num_r, Num_tm);
    for idx = 1:R
        r_idx = target_range(idx);
        spectrum_length = length(x_r{idx});
        center_start = round(spectrum_length/2 - Num_tm/2) + 1;
        center_end = center_start + Num_tm - 1;
        R_D_ANM(r_idx, :) = abs(x_r{idx}(center_start:center_end));
    end
    
end

%% 测试用的辅助函数
function [s_r_tm2, tm2, r, PRF, fc, B, c, Pos] = load_ship_data_test()
    
    % 船舶模型散射点位置 (简化版本)
    Pos = [-10 -1 0; -5 -1 0; 0 -1 0; 5 -1 0; 10 -1 0;
           -10  1 0; -5  1 0; 0  1 0; 5  1 0; 10  1 0;
           10.5 -0.75 0; 10.5 0.75 0; 11 0 0;
           5 0 2; 5 0 3;
           0 0 1; -5 0 1];
    
    % 雷达参数
    B = 80*1e6;
    c = 3*1e8;
    PRF = 1400;
    fc = 5.2*1e9;
    delta_r = c/(2*B);
    r = -50*delta_r:delta_r:50*delta_r;
    tm2 = 0:(1/PRF):0.2;  % 减少时间长度以加速测试
    
    % 生成简化的回波数据
    s_r_tm2 = generate_ship_echo_test(Pos, r, tm2, B, c, PRF, fc);
    
end

function s_r_tm2 = generate_ship_echo_test(Pos, r, tm2, B, c, PRF, fc)
    
    x_Pos = Pos(:,1)*5;
    y_Pos = Pos(:,2)*5;
    z_Pos = Pos(:,3)*5;
    
    R_vec = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    
    Num_point = size(Pos, 1);
    Num_r = length(r);
    Num_tm = length(tm2);
    
    s_r_tm2 = zeros(Num_r, Num_tm);
    
    for n_point = 1:Num_point
        Delta_R0 = x_Pos(n_point)*R_vec(1) + y_Pos(n_point)*R_vec(2) + z_Pos(n_point)*R_vec(3);
        
        % 简化的运动模型
        f = 0.1; alpha = 40; beita = 400;
        Delta_R = f.*tm2 + 0.5*alpha.*tm2.^2 + (1/6)*beita.*tm2.^3 + Delta_R0;
        sita_Delta_R = 4*pi*(fc/c) * Delta_R;
        
        ones_r = ones(Num_r, 1);
        ones_tm = ones(1, Num_tm);
        
        sinc_term = sinc((2*B/c) * (r.' * ones_tm - ones_r * Delta_R));
        phase_term = exp(1j * (ones_r * sita_Delta_R));
        
        s_r_tm2 = s_r_tm2 + sinc_term .* phase_term;
    end
    
    % 相位补偿
    phase_comp = exp(1j * 2*pi * (190.*tm2 + 0.5*40.*tm2.^2 + (1/6)*400.*tm2.^3));
    s_r_tm2 = s_r_tm2 .* repmat(phase_comp, Num_r, 1);
    
end

function entropy = EntropyImage_test(img)
    img_norm = img / sum(img(:));
    img_norm = img_norm(img_norm > 0);
    entropy = -sum(img_norm(:) .* log(img_norm(:)));
end

function c = contrast_test(img)
    img_norm = img / max(img(:));
    mean_val = mean(img_norm(:));
    c = std(img_norm(:)) / mean_val;
end 