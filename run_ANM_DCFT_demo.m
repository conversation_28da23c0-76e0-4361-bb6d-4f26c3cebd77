%% ANM-DCFT ISAR成像算法完整演示
%
% 本脚本演示基于原子范数最小化的DCFT ISAR成像算法
% 包括算法对比、性能分析和结果可视化
%
% 算法特点:
% 1. 联合优化相位误差和图像重构
% 2. 无网格化原子范数最小化
% 3. SDP松弛和ADMM求解
% 4. 空间平滑性约束
%
% 作者: AI Assistant
% 日期: 2024

clc; clear; close all;

fprintf('========================================\n');
fprintf('   ANM-DCFT ISAR成像算法完整演示\n');
fprintf('========================================\n');

%% 1. 数据准备
fprintf('\n1. 数据准备阶段\n');
fprintf('----------------------------------------\n');

% 检查是否存在数据文件
if exist('s_r_tm2.mat', 'file')
    fprintf('加载现有数据文件...\n');
    load('s_r_tm2.mat');
    if ~exist('tm', 'var')
        PRF = 1400;
        tm = 0:(1/PRF):0.501;
    end
else
    fprintf('运行原始DCFT算法生成数据...\n');
    % 运行原始算法
    run('Imaing_DCFT_Ship.m');
    % 保存数据
    save('s_r_tm2.mat', 's_r_tm2', 'tm');
end

fprintf('数据维度: %d x %d\n', size(s_r_tm2));
fprintf('时间采样点数: %d\n', length(tm));

%% 2. 算法参数配置
fprintf('\n2. 算法参数配置\n');
fprintf('----------------------------------------\n');

% 基础ANM-DCFT参数
basic_params = struct(...
    'lambda', 0.08, ...          % 稀疏正则化参数
    'mu', 0.02, ...              % 平滑正则化参数
    'max_iter', 25, ...          % 最大迭代次数
    'tol', 1e-4, ...             % 收敛容差
    'L_windows', 6, ...          % 频率窗口数量
    'alpha_range', [-32, 320], ... % 二次相位参数范围
    'beta_range', [-500, 2400], ... % 三次相位参数范围
    'alpha_step', 16, ...        % 二次相位搜索步长
    'beta_step', 200, ...        % 三次相位搜索步长
    'range_cells', 30:56 ...     % 处理的距离单元
);

% 高级ANM-DCFT参数
advanced_params = struct(...
    'lambda', 0.1, ...           % 稀疏正则化参数
    'mu', 0.01, ...              % 平滑正则化参数
    'rho', 1.0, ...              % ADMM惩罚参数
    'max_iter_outer', 15, ...    % 外层迭代次数
    'max_iter_inner', 20, ...    % 内层ADMM迭代次数
    'tol_outer', 1e-3, ...       % 外层收敛容差
    'tol_inner', 1e-4, ...       % 内层收敛容差
    'L_windows', 8, ...          % 频率窗口数量
    'alpha_range', [-32, 320], ... % 二次相位参数范围
    'beta_range', [-500, 2400], ... % 三次相位参数范围
    'alpha_step', 16, ...        % 二次相位搜索步长
    'beta_step', 200, ...        % 三次相位搜索步长
    'range_cells', 30:56, ...    % 处理的距离单元
    'use_tv_regularization', true, ... % 是否使用TV正则化
    'tv_weight', 0.05 ...        % TV正则化权重
);

fprintf('基础ANM-DCFT参数配置完成\n');
fprintf('高级ANM-DCFT参数配置完成\n');

%% 3. 运行算法对比
fprintf('\n3. 算法性能对比\n');
fprintf('----------------------------------------\n');

% 3.1 基础ANM-DCFT算法
fprintf('运行基础ANM-DCFT算法...\n');
tic;
[ISAR_basic, motion_basic, quality_basic, conv_basic] = ...
    ANM_DCFT_ISAR_Enhanced(s_r_tm2, tm, basic_params);
time_basic = toc;

% 3.2 高级ANM-DCFT算法
fprintf('运行高级ANM-DCFT算法...\n');
tic;
[ISAR_advanced, motion_advanced, quality_advanced, conv_advanced] = ...
    Advanced_ANM_DCFT_ISAR(s_r_tm2, tm, advanced_params);
time_advanced = toc;

% 3.3 原始DCFT算法（参考）
fprintf('运行原始DCFT算法作为参考...\n');
tic;
ISAR_original = run_original_DCFT(s_r_tm2, tm);
time_original = toc;

% 计算原始DCFT质量指标
quality_original = calculate_quality_metrics(ISAR_original);
quality_original.process_time = time_original;

%% 4. 结果分析和对比
fprintf('\n4. 结果分析和对比\n');
fprintf('----------------------------------------\n');

% 性能对比表
fprintf('\n算法性能对比:\n');
fprintf('%-15s %-10s %-10s %-10s %-10s\n', '算法', '对比度', '熵值', '聚焦度', '时间(s)');
fprintf('%-15s %-10.4f %-10.4f %-10.4f %-10.2f\n', ...
    '原始DCFT', quality_original.contrast, quality_original.entropy, ...
    quality_original.focus_measure, quality_original.process_time);
fprintf('%-15s %-10.4f %-10.4f %-10.4f %-10.2f\n', ...
    '基础ANM-DCFT', quality_basic.contrast, quality_basic.entropy, ...
    quality_basic.focus_measure, quality_basic.process_time);
fprintf('%-15s %-10.4f %-10.4f %-10.4f %-10.2f\n', ...
    '高级ANM-DCFT', quality_advanced.contrast, quality_advanced.entropy, ...
    quality_advanced.focus_measure, quality_advanced.process_time);

% 改进率计算
fprintf('\n改进率分析:\n');
contrast_imp_basic = (quality_basic.contrast - quality_original.contrast) / quality_original.contrast * 100;
entropy_imp_basic = (quality_original.entropy - quality_basic.entropy) / quality_original.entropy * 100;
focus_imp_basic = (quality_basic.focus_measure - quality_original.focus_measure) / quality_original.focus_measure * 100;

contrast_imp_advanced = (quality_advanced.contrast - quality_original.contrast) / quality_original.contrast * 100;
entropy_imp_advanced = (quality_original.entropy - quality_advanced.entropy) / quality_original.entropy * 100;
focus_imp_advanced = (quality_advanced.focus_measure - quality_original.focus_measure) / quality_original.focus_measure * 100;

fprintf('基础ANM-DCFT: 对比度 %+.1f%%, 熵值 %+.1f%%, 聚焦度 %+.1f%%\n', ...
    contrast_imp_basic, entropy_imp_basic, focus_imp_basic);
fprintf('高级ANM-DCFT: 对比度 %+.1f%%, 熵值 %+.1f%%, 聚焦度 %+.1f%%\n', ...
    contrast_imp_advanced, entropy_imp_advanced, focus_imp_advanced);

%% 5. 可视化结果
fprintf('\n5. 生成可视化结果\n');
fprintf('----------------------------------------\n');

% 5.1 成像结果对比
figure('Name', '三种算法成像结果对比', 'Position', [50, 50, 1400, 500]);

% 原始DCFT
subplot(1, 3, 1);
G1 = 20*log10(abs(ISAR_original)./max(abs(ISAR_original(:))));
imagesc(G1); caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title(sprintf('原始DCFT\n对比度: %.3f, 熵: %.3f', quality_original.contrast, quality_original.entropy));
colormap jet;

% 基础ANM-DCFT
subplot(1, 3, 2);
G2 = 20*log10(abs(ISAR_basic)./max(abs(ISAR_basic(:))));
imagesc(G2); caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title(sprintf('基础ANM-DCFT\n对比度: %.3f, 熵: %.3f', quality_basic.contrast, quality_basic.entropy));
colormap jet;

% 高级ANM-DCFT
subplot(1, 3, 3);
G3 = 20*log10(abs(ISAR_advanced)./max(abs(ISAR_advanced(:))));
imagesc(G3); caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title(sprintf('高级ANM-DCFT\n对比度: %.3f, 熵: %.3f', quality_advanced.contrast, quality_advanced.entropy));
colormap jet;

% 5.2 质量指标对比
figure('Name', '质量指标对比', 'Position', [50, 600, 1000, 400]);

algorithms = {'原始DCFT', '基础ANM-DCFT', '高级ANM-DCFT'};
contrasts = [quality_original.contrast, quality_basic.contrast, quality_advanced.contrast];
entropies = [quality_original.entropy, quality_basic.entropy, quality_advanced.entropy];
focuses = [quality_original.focus_measure, quality_basic.focus_measure, quality_advanced.focus_measure];

subplot(1, 3, 1);
bar(contrasts); set(gca, 'XTickLabel', algorithms);
ylabel('对比度'); title('对比度对比'); grid on;

subplot(1, 3, 2);
bar(entropies); set(gca, 'XTickLabel', algorithms);
ylabel('熵值'); title('熵值对比 (越低越好)'); grid on;

subplot(1, 3, 3);
bar(focuses); set(gca, 'XTickLabel', algorithms);
ylabel('聚焦度量'); title('聚焦度量对比'); grid on;

% 5.3 运动参数对比
if isfield(motion_basic, 'alpha') && isfield(motion_advanced, 'alpha')
    figure('Name', '运动参数估计对比', 'Position', [50, 1100, 1000, 400]);
    
    subplot(1, 2, 1);
    plot(basic_params.range_cells, motion_basic.alpha, 'b-o', 'LineWidth', 2, 'DisplayName', '基础ANM-DCFT');
    hold on;
    plot(advanced_params.range_cells, motion_advanced.alpha, 'r-s', 'LineWidth', 2, 'DisplayName', '高级ANM-DCFT');
    grid on; xlabel('距离单元'); ylabel('α (二次相位参数)');
    title('二次相位参数估计'); legend('Location', 'best');
    
    subplot(1, 2, 2);
    plot(basic_params.range_cells, motion_basic.beta, 'b-o', 'LineWidth', 2, 'DisplayName', '基础ANM-DCFT');
    hold on;
    plot(advanced_params.range_cells, motion_advanced.beta, 'r-s', 'LineWidth', 2, 'DisplayName', '高级ANM-DCFT');
    grid on; xlabel('距离单元'); ylabel('β (三次相位参数)');
    title('三次相位参数估计'); legend('Location', 'best');
end

%% 6. 保存结果
fprintf('\n6. 保存结果\n');
fprintf('----------------------------------------\n');

demo_results = struct(...
    'algorithms', struct(...
        'original', struct('image', ISAR_original, 'quality', quality_original), ...
        'basic_anm', struct('image', ISAR_basic, 'quality', quality_basic, 'motion', motion_basic), ...
        'advanced_anm', struct('image', ISAR_advanced, 'quality', quality_advanced, 'motion', motion_advanced)), ...
    'parameters', struct('basic', basic_params, 'advanced', advanced_params), ...
    'improvements', struct(...
        'basic_contrast', contrast_imp_basic, 'basic_entropy', entropy_imp_basic, 'basic_focus', focus_imp_basic, ...
        'advanced_contrast', contrast_imp_advanced, 'advanced_entropy', entropy_imp_advanced, 'advanced_focus', focus_imp_advanced) ...
);

save('ANM_DCFT_demo_results.mat', 'demo_results');
fprintf('演示结果已保存至 ANM_DCFT_demo_results.mat\n');

fprintf('\n========================================\n');
fprintf('   ANM-DCFT演示完成\n');
fprintf('========================================\n');

%% ==================== 辅助函数 ====================

function ISAR_image = run_original_DCFT(s_r_tm2, tm)
%% 运行原始DCFT算法

[Num_r, Num_tm] = size(s_r_tm2);
FactorDop = 1;
ISAR_image = zeros(Num_r, FactorDop*Num_tm);

for n_delta_r = 30:56
    S_f = zeros(1, FactorDop*Num_tm);
    s_tm_temp = s_r_tm2(n_delta_r, :);
    
    % 参数搜索
    alpha_sea = -16:8:320;
    beita_sea = -500:100:2400;
    
    % 寻找最大值
    ISAR_f_a_b_max = 0;
    for n_alpha = 1:length(alpha_sea)
        for n_beita = 1:length(beita_sea)
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beita_sea(n_beita)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp);
            if max(abs(S_dechirp)) > ISAR_f_a_b_max
                ISAR_f_a_b_max = max(abs(S_dechirp));
            end
        end
    end
    
    % 计算频谱
    for n_alpha = 1:length(alpha_sea)
        for n_beita = 1:length(beita_sea)
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beita_sea(n_beita)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp, FactorDop*Num_tm);
            
            for n_tm = 1:FactorDop*Num_tm
                if abs(S_dechirp(n_tm)) > (ISAR_f_a_b_max*0.0)
                    S_f(n_tm) = S_f(n_tm) + S_dechirp(n_tm);
                end
            end
        end
    end
    
    ISAR_image(n_delta_r, :) = abs(S_f);
end

end

function quality = calculate_quality_metrics(image)
%% 计算图像质量指标

image_norm = image / max(image(:));

% 对比度
image_abs = abs(image_norm);
mean_val = mean(image_abs(:));
std_val = std(image_abs(:));
contrast = std_val / mean_val;

% 熵值
image_power = image_abs.^2;
total_power = sum(image_power(:));
normalized_power = image_power / total_power;
valid_idx = normalized_power > eps;
entropy = -sum(normalized_power(valid_idx) .* log2(normalized_power(valid_idx)));

% 聚焦度量
[Gx, Gy] = gradient(image_abs);
focus_measure = var(sqrt(Gx.^2 + Gy.^2), [], 'all');

quality = struct('contrast', contrast, 'entropy', entropy, 'focus_measure', focus_measure);

end
