%% 测试修正后的ANM-DCFT算法
%
% 本脚本用于测试修正后的ANM-DCFT算法，确保正确集成DCFT思想
%
% 作者: AI Assistant
% 日期: 2024

clc; clear; close all;

fprintf('========== 测试修正后的ANM-DCFT算法 ==========\n');

%% 1. 数据准备
fprintf('1. 准备测试数据...\n');

% 检查是否存在数据文件
if exist('s_r_tm2.mat', 'file')
    fprintf('   加载现有数据文件...\n');
    load('s_r_tm2.mat');
    if ~exist('tm', 'var')
        PRF = 1400;
        tm = 0:(1/PRF):0.501;
    end
else
    fprintf('   运行原始DCFT算法生成数据...\n');
    % 运行原始算法
    run('Imaing_DCFT_Ship.m');
    % 保存数据
    save('s_r_tm2.mat', 's_r_tm2', 'tm');
end

fprintf('   数据维度: %d x %d\n', size(s_r_tm2));

%% 2. 设置算法参数
fprintf('2. 设置算法参数...\n');

% ANM-DCFT参数（与原始DCFT保持一致的搜索范围）
anm_params = struct(...
    'lambda', 0.1, ...           % ANM正则化参数
    'range_cells', 30:56, ...    % 处理的距离单元
    'delta_alpha', 8, ...        % 二次相位搜索步长
    'alpha_min', -16, ...        % 二次相位最小值
    'alpha_max', 320, ...        % 二次相位最大值
    'delta_beta', 100, ...       % 三次相位搜索步长
    'beta_min', -500, ...        % 三次相位最小值
    'beta_max', 2400, ...        % 三次相位最大值
    'FactorDop', 1 ...           % Doppler插值因子
);

fprintf('   参数设置完成\n');

%% 3. 运行原始DCFT算法作为参考
fprintf('3. 运行原始DCFT算法...\n');
tic;

% 运行原始DCFT处理
FactorDop = 1;
Num_tm = size(s_r_tm2, 2);
Num_r = size(s_r_tm2, 1);
R_D_DCFT_original = zeros(Num_r, FactorDop*Num_tm);

for n_delta_r = 30:56
    S_f = zeros(1, FactorDop*Num_tm);
    s_tm_temp = s_r_tm2(n_delta_r, :);
    
    % 参数搜索范围
    delta_alpha = 8;
    alpha_sea = -16:delta_alpha:320;
    delta_beita = 100;
    beita_sea = -500:delta_beita:2400;
    
    % 寻找最大值
    ISAR_f_a_b_max = 0;
    for n_alpha = 1:length(alpha_sea)
        for n_beita = 1:length(beita_sea)
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beita_sea(n_beita)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp);
            if max(abs(S_dechirp)) > ISAR_f_a_b_max
                ISAR_f_a_b_max = max(abs(S_dechirp));
            end
        end
    end
    
    % 计算Doppler分布
    for n_alpha = 1:length(alpha_sea)
        for n_beita = 1:length(beita_sea)
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beita_sea(n_beita)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp, FactorDop*Num_tm);
            
            for n_tm = 1:FactorDop*Num_tm
                if abs(S_dechirp(n_tm)) > (ISAR_f_a_b_max*0.0)
                    S_f(n_tm) = S_f(n_tm) + S_dechirp(n_tm);
                end
            end
        end
    end
    
    R_D_DCFT_original(n_delta_r, :) = abs(S_f);
end

dcft_time = toc;
fprintf('   原始DCFT处理完成，耗时: %.2f 秒\n', dcft_time);

%% 4. 运行修正后的ANM-DCFT算法
fprintf('4. 运行修正后的ANM-DCFT算法...\n');

[R_D_ANM_DCFT, quality_metrics_anm, motion_params_anm] = ...
    ANM_DCFT_ISAR_Fixed(s_r_tm2, tm, anm_params);

%% 5. 计算原始DCFT的质量指标
fprintf('5. 计算质量指标对比...\n');

% 原始DCFT质量指标
R_D_DCFT_norm = R_D_DCFT_original / max(R_D_DCFT_original(:));
contrast_original = calculate_contrast_test(R_D_DCFT_norm);
entropy_original = calculate_entropy_test(R_D_DCFT_norm);
focus_original = calculate_focus_test(R_D_DCFT_norm);

quality_metrics_original = struct(...
    'contrast', contrast_original, ...
    'entropy', entropy_original, ...
    'focus_measure', focus_original, ...
    'process_time', dcft_time ...
);

%% 6. 结果对比分析
fprintf('\n========== 算法性能对比 ==========\n');
fprintf('指标                  原始DCFT    ANM-DCFT    改进率\n');
fprintf('----------------------------------------------------\n');

% 对比度
contrast_improvement = (quality_metrics_anm.contrast - contrast_original) / contrast_original * 100;
fprintf('对比度                %.4f      %.4f      %+.1f%%\n', ...
    contrast_original, quality_metrics_anm.contrast, contrast_improvement);

% 熵值（越低越好）
entropy_improvement = (entropy_original - quality_metrics_anm.entropy) / entropy_original * 100;
fprintf('熵值                  %.4f      %.4f      %+.1f%%\n', ...
    entropy_original, quality_metrics_anm.entropy, entropy_improvement);

% 聚焦度量
focus_improvement = (quality_metrics_anm.focus_measure - focus_original) / focus_original * 100;
fprintf('聚焦度量              %.4f      %.4f      %+.1f%%\n', ...
    focus_original, quality_metrics_anm.focus_measure, focus_improvement);

% 处理时间
time_ratio = quality_metrics_anm.process_time / dcft_time;
fprintf('处理时间(秒)          %.2f       %.2f       %.1fx\n', ...
    dcft_time, quality_metrics_anm.process_time, time_ratio);

%% 7. 可视化对比结果
fprintf('\n7. 生成对比图像...\n');

% 成像结果对比
figure('Name', '修正后ANM-DCFT vs 原始DCFT', 'Position', [100, 100, 1200, 500]);

% 原始DCFT结果
subplot(1, 2, 1);
G1_original = 20*log10(abs(R_D_DCFT_original)./max(abs(R_D_DCFT_original(:))));
imagesc(G1_original);
caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title(sprintf('原始DCFT\n对比度: %.3f, 熵: %.3f', contrast_original, entropy_original));
colormap jet;

% ANM-DCFT结果
subplot(1, 2, 2);
G1_anm = 20*log10(abs(R_D_ANM_DCFT)./max(abs(R_D_ANM_DCFT(:))));
imagesc(G1_anm);
caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title(sprintf('修正ANM-DCFT\n对比度: %.3f, 熵: %.3f', ...
    quality_metrics_anm.contrast, quality_metrics_anm.entropy));
colormap jet;

% 质量指标对比图
figure('Name', '质量指标对比', 'Position', [100, 600, 800, 400]);

metrics_names = {'对比度', '熵值', '聚焦度量'};
original_values = [contrast_original, entropy_original, focus_original];
anm_values = [quality_metrics_anm.contrast, quality_metrics_anm.entropy, quality_metrics_anm.focus_measure];

x = 1:3;
width = 0.35;

bar(x - width/2, original_values, width, 'DisplayName', '原始DCFT');
hold on;
bar(x + width/2, anm_values, width, 'DisplayName', '修正ANM-DCFT');

set(gca, 'XTickLabel', metrics_names);
ylabel('指标值');
title('算法性能对比');
legend('Location', 'best');
grid on;

%% 8. 检查成像是否成功
fprintf('\n8. 成像结果检查...\n');

% 检查图像是否有有效内容
max_val_original = max(abs(R_D_DCFT_original(:)));
max_val_anm = max(abs(R_D_ANM_DCFT(:)));

fprintf('原始DCFT最大值: %.2e\n', max_val_original);
fprintf('ANM-DCFT最大值: %.2e\n', max_val_anm);

if max_val_anm > max_val_original * 0.1
    fprintf('✓ ANM-DCFT成像成功！\n');
else
    fprintf('✗ ANM-DCFT成像可能存在问题\n');
end

%% 9. 保存结果
fprintf('9. 保存测试结果...\n');

test_results = struct(...
    'original_dcft', struct('image', R_D_DCFT_original, 'quality', quality_metrics_original), ...
    'anm_dcft', struct('image', R_D_ANM_DCFT, 'quality', quality_metrics_anm, 'motion', motion_params_anm), ...
    'improvements', struct(...
        'contrast', contrast_improvement, ...
        'entropy', entropy_improvement, ...
        'focus', focus_improvement, ...
        'time_ratio', time_ratio), ...
    'parameters', anm_params ...
);

save('ANM_DCFT_fixed_test_results.mat', 'test_results');
fprintf('   测试结果已保存至 ANM_DCFT_fixed_test_results.mat\n');

fprintf('\n========== 测试完成 ==========\n');

%% ==================== 辅助函数 ====================

function contrast = calculate_contrast_test(image)
    image_abs = abs(image);
    mean_val = mean(image_abs(:));
    std_val = std(image_abs(:));
    if mean_val == 0
        contrast = 0;
    else
        contrast = std_val / mean_val;
    end
end

function entropy = calculate_entropy_test(image)
    image_abs = abs(image);
    image_norm = image_abs / sum(image_abs(:));
    valid_idx = image_norm > eps;
    if ~any(valid_idx)
        entropy = 0;
    else
        entropy = -sum(image_norm(valid_idx) .* log2(image_norm(valid_idx)));
    end
end

function focus = calculate_focus_test(image)
    image_abs = abs(image);
    [Gx, Gy] = gradient(image_abs);
    focus = var(sqrt(Gx.^2 + Gy.^2), [], 'all');
end
