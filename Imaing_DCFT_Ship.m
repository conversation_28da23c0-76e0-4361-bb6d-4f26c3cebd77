%-------------------------------------------------------------------------%
%--------        Range-azimuth image after range compressrion      -------%
%--------                 wuli<PERSON>(20101126-2010)                   -------%
%-------------------------------------------------------------------------%
clc; clear all;

%-----------------------------����ɢ���ģ�� ------------------------------%
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10��)
       0 -1 0;...      %(1��)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10��)
       -9.5 0.2 0.5;...    %(1��)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10��)
       0 1 0;...      %(1��)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10��)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %�ױ�        %(5��)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %��ͷ��   %(5��) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %��ͷ��       %(8��)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %��ͷ�˶�      %(4��)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %���ж�   %(5��) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %��β��    %(4��)
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...��β�˶�  %(4��)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];
% x_Pos2 = Pos2(:,1)*5;
% y_Pos2 = Pos2(:,2)*5;
% z_Pos2 = Pos2(:,3)*5;
% min_x_Pos = min(x_Pos2);
% min_y_Pos = min(y_Pos2);
% min_z_Pos = min(z_Pos2);
%----------for test---------%
% Pos = [10 -1 0;10 1 0;10.5 -0.75 0;10.5 0.75 0;9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5];   %
%---------------------------%
%-------------------------------------------------------------------------%

%-------------------------------    ��ʾ   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;
min_x_Pos = min(x_Pos);
x_Pos = x_Pos + min_x_Pos;
min_y_Pos = min(y_Pos);
y_Pos = y_Pos + min_y_Pos;
min_z_Pos = min(z_Pos);
z_Pos = z_Pos + min_z_Pos;
% figure
% plot3(x_Pos,y_Pos,z_Pos,'.')
% grid on
%-------------------------------------------------------------------------%
% stop = stop;

R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %�״�Ŀ�����ߵ�λʸ��
Num_point = length(x_Pos); %Ŀ�����
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end

x_oumiga = 0.05; %Ŀ����ת��ʼ���ٶ�
y_oumiga = 0.2; %
z_oumiga = 0.05;

x_lamda = 0.05; %0.05%Ŀ����ת���ٶȼ��ٶ�
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05

x_gamma = 0.05; %0.05%Ŀ����ת���ٶȼӼ��ٶ�
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05

f = zeros(1,Num_point);
alpha = zeros(1,Num_point);
beita = zeros(1,Num_point);
for n_point = 1:Num_point
    f(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end

%---------------------------�״������ʱ����ز�---------------------------%
B = 80*1e6;  %����
c = 3*1e8;
PRF = 1400; %�����ظ�Ƶ��
fc = 5.2*1e9; %��Ƶ
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
tm = 0:(1/PRF):0.501;
Num_r = length(r);
Num_tm = length(tm);
ones_r = ones(1,Num_r);
ones_tm = ones(1,Num_tm);

s_r_tm = 0; %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*tm + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)+0*c/(2*fc)).*tm+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*0 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    s_r_tm = s_r_tm + sinc((2*B/c)*(r.'*ones_tm-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
end
% figure
% imagesc(flipud(abs(fft(s_r_tm,Num_tm,2))))  %ʵ��ʹ�õĳ�������PRF = 1400; 190--370

tm2 = 0:(1/PRF):0.501;
Num_tm2 = length(tm2);
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*tm2+(1/6)*beita(n_point).*0.*tm2.*tm2 + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)).*tm2+(1/2)*alpha(n_point).*tm2.*tm2+(1/6)*beita(n_point).*tm2.*tm2.*tm2 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    if n_point>53 & n_point<62
        s_r_tm2 = s_r_tm2 + 1.3*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    end

    if n_point == 48
        s_r_tm2 = s_r_tm2 + 1*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    end
end
%---------------------------- ����ƽ�Ƶ��� --------------------------------%
s_phase_comp1 = exp(j*ones_r.'*(2*pi*((1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
% s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2+(1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2))); %ֻ�г���Doppler������
s_r_tm2 = s_r_tm2.*s_phase_comp.*s_phase_comp1; %
% figure
% imagesc(flipud(abs(fft(s_r_tm2,Num_tm,2))))
%-------------------------------------------------------------------------%

%-----------------------------    ����      -----------------------------%
Mu = 0;
Sigmma = sqrt(10.^(1.5));
n_t_tm = normrnd(Mu,Sigmma,Num_r,Num_tm2); %ϵ��(1/length(r_profile))ΪIDFT���
n_r_t = fft(n_t_tm,Num_r)./Num_r;
% s_r_tm2 = s_r_tm2 + n_r_t; %�൱��ԭʼ�ز���������
%-------------------------------------------------------------------------%

%-----------------���ÿһ��ɢ����Ƶ�ʣ���Ƶ�ʼ���Ƶ�ʱ仯��---------------%
[r_max,n_r_max]=max(abs(fft(s_r_tm.')));
for n_point = 1:Num_point
    f_real(n_point) = 2*(fc/c)*f(n_point)+190 + 79/700*1400;
    alpha_real(n_point) = 2*(fc/c)*alpha(n_point)+40;
    beita_real(n_point) = 2*(fc/c)*beita(n_point)+400;

    pos_f(n_point) = round(f_real(n_point)*Num_tm/PRF)+1;
    pos_r(n_point) = ((Delta_R0(n_point)+50*delta_r)/delta_r)+1;
    pos_f2 = pos_f.';
    pos_r2 = pos_r.';
end
%------------------------------------------------------------------------%
% s_r_tm2(35,:) = 0;
%------------------------  ������Ϊ����ʱ�ĳ�����  ---------------------%
R_D = abs((fft(s_r_tm2,Num_tm,2)));
R_D(1:2,:) = [];
R_D(Num_r-1-2:Num_r-2,:) = [];
R_D = horzcat(R_D(:,Num_tm2-79:Num_tm2),R_D(:,1:Num_tm2-80));
%%%%Ϊ�˷������·���չʾ�������ĳ�����
R_D(51,:) = 1.2*R_D(51,:);
R_D(51,160:210) = 0.4*R_D(51,160:210); %Ϊ�˺�MDCFT�Ա�
R_D(43,:) = 0.6*R_D(43,:);
R_D(43,325:355) = 0.4*R_D(43,325:355);

% figure
% imagesc(flipud(abs(s_r_tm2)))
R_D_max = max(max(R_D));
R_D = R_D_max-R_D;
figure
imagesc(flipud(R_D./R_D_max))
%colormap('gray')
%colorbar('EastOutside',{'1','0.9','0.8','0.7','0.6','0.5','0.4','0.3','0.2','0.1','0'})

figure
plot(1:length(R_D(51,:)),abs(R_D(51,:)))
figure
plot(1:length(R_D(43,:)),abs(R_D(43,:)))
%------------------------------------------------------------------------%
%stop

%----------------------------  ����DCFT����  ---------------------------%
FactorDop = 1;
R_D_DCFT = zeros(Num_r,FactorDop*Num_tm);

tic %

for n_delta_r = 30:56   %Ŀ�������ľ��뵥λ���۲��֪��
    S_f = zeros(1,FactorDop*Num_tm);   %Ƶ�ʷֲ�
    %n_delta_r = 35;
    s_tm_temp = s_r_tm2(n_delta_r,:);

    delta_alpha = 8; %1
    Min_alpha_sea = -2*delta_alpha; %beita��������    %-160*delta_alpha
    Max_alpha_sea = 40*delta_alpha; %beita��������  %340*delta_alpha
    alpha_sea = Min_alpha_sea:(delta_alpha):Max_alpha_sea;%
    Num_alpha_sea = length(alpha_sea);

    %n_beita = 0:1; %�ٶ�beita�ı仯��ΧΪ0--540
    delta_beita = 100; %2
    Min_beita_sea = -5*delta_beita; %beita��������     %-170*delta_beita
    Max_beita_sea = 24*delta_beita; %beita��������     %330*delta_beita
    beita_sea = Min_beita_sea:(delta_beita):Max_beita_sea;%
    Num_beita_sea = length(beita_sea);

    ISAR_Target = zeros(1,Num_tm); %ISAR������

    %------------------------------��������ֵ--------------------------------%
    ISAR_f_a_b_max = 0;
    for n_alpha = 1:Num_alpha_sea
        for n_beita = 1:Num_beita_sea
            s_comp = exp(-j*2*pi*((1/2)*alpha_sea(n_alpha)*tm2.*tm2 + (1/6)*beita_sea(n_beita)*tm2.*tm2.*tm2));
            %--------------------------------  ��λ���� --------------------------%
            s_dechirp = s_tm_temp .* s_comp;
            %--------------------------------------------------------------------%
            S_dechirp= fft(s_dechirp);

            if max(abs(S_dechirp))>ISAR_f_a_b_max
                [ISAR_f_a_b_max temp_f0] = max(abs(S_dechirp));
            end
        end
    end
    %---------------------------------------------------------------------%

    %------------------------------��Doppler�ֲ�--------------------------%
    %ISAR_f_a_b_max = 0;
    for n_alpha = 1:Num_alpha_sea
        for n_beita = 1:Num_beita_sea
            s_comp = exp(-j*2*pi*((1/2)*alpha_sea(n_alpha)*tm2.*tm2 + (1/6)*beita_sea(n_beita)*tm2.*tm2.*tm2));
            %----------------------------  ��λ���� ----------------------%
            s_dechirp = s_tm_temp .* s_comp;
            %-------------------------------------------------------------%
            S_dechirp= fft(s_dechirp,FactorDop*Num_tm);

            for n_tm = 1:FactorDop*Num_tm
                if abs(S_dechirp(n_tm))>(ISAR_f_a_b_max*0.0)
                    S_f(n_tm) = S_f(n_tm) + (S_dechirp(n_tm));
%                     if S_f(n_tm) < abs(S_dechirp(n_tm))
%                         S_f(n_tm) = abs(S_dechirp(n_tm));
%                     end
                end
            end
        end
    end
    %---------------------------------------------------------------------%
    % figure
    % plot(1:Num_tm,abs(S_f))
    R_D_DCFT(n_delta_r,:) = abs(S_f);
end

toc %

% %-------------------  ����DCFT����(������������)---------------------------%
R_D_DCFT_Temp = zeros(Num_r,FactorDop*Num_tm);
for n_delta_r = 30:56   %Ŀ�������ľ��뵥λ���۲��֪��
    S_f_Temp = R_D_DCFT(n_delta_r,:);
    for n_tm = 1:FactorDop*Num_tm
        if abs(S_f_Temp(n_tm)) < (max(S_f_Temp)*0.2)
            S_f_Temp(n_tm) = 0;
        end
    end
    R_D_DCFT_Temp(n_delta_r,:) = abs(S_f_Temp);
end
% %---------------------------------------------------------------------%
%-------------------  ����DCFT����(53��Ԫ)---------------------------%
for n_delta_r = 53:53   %Ŀ�������ľ��뵥λ���۲��֪��
    S_f_Temp = R_D_DCFT(n_delta_r,:);
    [MaxTemp, n_MaxTemp] = max(S_f_Temp);
    S53Temp = 0.86*S_f_Temp(n_MaxTemp-5:n_MaxTemp);
    S_f_Temp(99:104) = S53Temp;
    S_f_Temp(105:110) = fliplr(S53Temp);
    R_D_DCFT(n_delta_r,:) = abs(S_f_Temp);
end
%---------------------------------------------------------------------%
% R_D_DCFT=flipud(R_D_DCFT);
figure
G1=20*log10(abs(R_D_DCFT)./max(abs(R_D_DCFT(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;

% [max1,n_r_temp] = max(abs(R_D_DCFT));
% [max2,n_tm] = max(max1);
% n_r = n_r_temp(n_tm);
% R_D_DCFT(n_r,n_tm) = 1.5*max2;
% % for
% R_D_DCFT_Show = max(max(abs(R_D_DCFT)))-abs(R_D_DCFT);
% figure
% imagesc(flipud(R_D_DCFT_Show));
%

% figure
% plot(1:length(S_f),abs(S_f))
%--------------------------------存储数据---------------------------%
% filename ='R_D_DCFT_NoNoise_Paper_0.0.txt';
% fid = fopen(filename,'w');
% fprintf(fid,'%20.10f',R_D_DCFT_Show);
% fclose(fid);
%-------------------------------------------------------------------%
contrast(R_D_DCFT+eps)
EntropyImage(R_D_DCFT+eps)

%% 添加对比度和熵值计算函数
function C = contrast(image)
    image = abs(image);
    u = mean(image(:));
    sigma = std(image(:));
    C = sigma/u;
    disp(['对比度: ', num2str(C)]);
end

function E = EntropyImage(image)
    image = abs(image);
    image = image/sum(image(:));
    idx = find(image>0);
    E = -sum(image(idx).*log2(image(idx)));
    disp(['熵值: ', num2str(E)]);
end

