%--------------------------------------------------------------------------
% 基于原子范数最小化(ANM)和快速交替方向乘子法(ADMM)的ISAR高分辨率成像算法
%
% 功能: 
%   根据用户提出的联合优化模型，实现高阶运动补偿与高分辨率成像。
%   此版本使用高效的ADMM算法求解原子范数子问题，替代了原有的慢速CVX实现。
%
% 作者: Gemini
%--------------------------------------------------------------------------

%% 1. 参数定义和信号仿真 (采用 Imaing_DCFT_Ship.m 中的模型)
clc; clear; close all;
fprintf('### 1. 开始仿真ISAR回波信号 (基于舰船模型) ###\n');

% --- 目标散射点模型 ---
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
       0 -1 0;...
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
       -9.5 0.2 0.5;...
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
       0 1 0;...
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;];
x_Pos = Pos(:,1)*5; y_Pos = Pos(:,2)*5; z_Pos = Pos(:,3)*5;
Num_point = length(x_Pos);

% --- 雷达和运动参数 ---
c = 3e8; fc = 5.2e9; PRF = 1400; B = 80e6;
tm = 0:(1/PRF):0.501;
M = length(tm);
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
R_num = length(r);

R_axis = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];
x_oumiga = 0.05; y_oumiga = 0.2; z_oumiga = 0.05;
x_lamda = 0.05; y_lamda = 0.1; z_lamda = 0.05;
x_gamma = 0.05; y_gamma = 0.4; z_gamma = 0.05;

x_r = y_Pos*R_axis(3)-z_Pos*R_axis(2);
y_r = z_Pos*R_axis(1)-x_Pos*R_axis(3);
z_r = x_Pos*R_axis(2)-y_Pos*R_axis(1);

f_d_all = x_r*x_oumiga+y_r*y_oumiga+z_r*z_oumiga;
alpha_d_all = x_r*x_lamda+y_r*y_lamda+z_r*z_lamda;
beta_d_all = x_r*x_gamma+y_r*y_gamma+z_r*z_gamma;

% --- 生成回波信号 ---
s_r_tm = zeros(R_num, M);
for n_point = 1:Num_point
    Delta_R0 = x_Pos(n_point)*R_axis(1)+y_Pos(n_point)*R_axis(2)+z_Pos(n_point)*R_axis(3);
    Delta_R = Delta_R0 + f_d_all(n_point).*tm + (1/2)*alpha_d_all(n_point).*tm.^2 + (1/6)*beta_d_all(n_point).*tm.^3;
    
    phase = 4*pi*fc/c * Delta_R;
    sinc_term = sinc((2*B/c)*(r' * ones(1, M) - ones(R_num, 1) * Delta_R));
    s_r_tm = s_r_tm + sinc_term .* exp(1j * phase);
end
fprintf('回波信号生成完毕。\n');
fprintf('信号矩阵大小: %d (距离单元) x %d (脉冲数)\n', R_num, M);

%% 2. 基于ANM-ADMM的成像算法
fprintf('\n### 2. 开始执行基于ANM-ADMM的成像算法 ###\n');
% --- 算法参数 ---
lambda = 0.1;       % 原子范数正则化参数 (控制稀疏性)
mu = 50;            % 运动参数平滑性正则化参数
n_iter = 10;        % 外部ADMM迭代次数
step_size = 1e-8;   % 运动参数更新步长 (需要根据梯度大小小心调整)

% --- 变量初始化 ---
x_r = s_r_tm; 
alpha_est = zeros(R_num, 1);
beta_est = zeros(R_num, 1);
cost_history = zeros(n_iter, 1);

% --- ADMM 主循环 ---
tic;
for iter = 1:n_iter
    fprintf('\n--- ADMM 迭代: %d / %d ---\n', iter, n_iter);
    
    % --- Step 1: 更新图像 x_r (使用快速ADMM求解器) ---
    fprintf('  Step 1: 更新图像 x_r ...\n');
    phase_comp_mat = exp(-1j * (2*pi) * (0.5 * alpha_est * tm.^2 + (1/6) * beta_est * tm.^3));
    s_r_tm_comp = s_r_tm .* phase_comp_mat;
    
    parfor r = 1:R_num
        if norm(s_r_tm_comp(r, :)) > 0.05 * max(vec(abs(s_r_tm_comp)))
             x_r(r, :) = solve_anm_denoising_admm(s_r_tm_comp(r, :).', M, lambda);
        else
             x_r(r, :) = zeros(1, M);
        end
    end
    fprintf('  Step 1 完成。\n');

    % --- Step 2: 更新运动参数 alpha, beta ---
    fprintf('  Step 2: 使用梯度下降更新运动参数 alpha, beta...\n');
    [grad_alpha, grad_beta] = compute_gradients(s_r_tm, x_r, alpha_est, beta_est, tm, mu);
    alpha_est = alpha_est - step_size * grad_alpha;
    beta_est = beta_est - step_size * grad_beta;
    fprintf('  Step 2 完成。\n');
    
    % --- 计算并记录当前代价函数值 ---
    phase_est_mat = exp(1j * (2*pi) * (0.5 * alpha_est * tm.^2 + (1/6) * beta_est * tm.^3));
    data_fidelity = norm(s_r_tm - x_r .* phase_est_mat, 'fro')^2;
    smooth_term = mu * (norm(diff(alpha_est))^2 + norm(diff(beta_est))^2);
    cost_history(iter) = data_fidelity + smooth_term;
    fprintf('  迭代 %d, 代价函数值 (近似) = %e\n', iter, cost_history(iter));
end
total_time = toc;
fprintf('\n### 算法执行完毕，总耗时: %.2f 秒 ###\n', total_time);

%% 3. 结果可视化
fprintf('\n### 3. 显示成像结果 ###\n');
phase_final = exp(1j * (2*pi) * (0.5 * alpha_est * tm.^2 + (1/6) * beta_est * tm.^3));
ISAR_image_anm = fftshift(fft(x_r .* phase_final, [], 2), 2);

figure;
imagesc(db(abs(ISAR_image_anm)./max(abs(ISAR_image_anm(:)))));
caxis([-40, 0]);
title('基于快速ADMM的高分辨率ISAR成像结果');
xlabel('多普勒单元');
ylabel('距离单元');
colormap('jet');
colorbar;

figure;
plot(1:n_iter, cost_history, '-o');
title('代价函数收敛曲线');
xlabel('迭代次数');
ylabel('代价函数值');
grid on;

%% 辅助函数
% -------------------------------------------------------------------------
% 函数: solve_anm_denoising_admm
% 功能: 使用高效的ADMM算法求解原子范数去噪问题
%       min_{x} 0.5 * ||y - x||_2^2 + lambda * ||x||_A
% -------------------------------------------------------------------------
function x = solve_anm_denoising_admm(y, M, lambda)
    % ADMM参数
    rho = 1;
    max_iter = 100;
    tol = 1e-4;

    % 初始化变量
    x = y;
    u = zeros(M, 1);
    t = 0;
    Z = zeros(M + 1, M + 1);
    Lambda = zeros(M + 1, M + 1);

    for iter = 1:max_iter
        Z_old = Z;
        
        % Step 1: (x, u, t) 更新
        W = Z - Lambda / rho;
        
        % x 更新
        w_col = W(1:M, M+1);
        w_row = W(M+1, 1:M);
        x = (y + rho * (w_col + w_row.')) / (1 + 2*rho);

        % t 更新
        t = W(M+1, M+1) - lambda / (2*rho);

        % u 更新
        W_sub = W(1:M, 1:M);
        u(1) = (trace(W_sub) - lambda / (2*rho)) / M;
        for k = 1:(M-1)
            u(k+1) = trace(W_sub, k) / (M-k);
        end
        
        % Step 2: Z 更新 (投影到半正定锥)
        T = toeplitz(u);
        Z1 = [T, x; x', t];
        Z_update = Z1 + Lambda/rho;
        
        [V, D] = eig(Z_update);
        D(D<0) = 0;
        Z = V * D * V';
        Z = (Z + Z') / 2; % 保证厄米性

        % Step 3: 对偶变量更新
        Lambda = Lambda + rho * (Z1 - Z);
        
        % 检查收敛
        primal_res = norm(Z1 - Z, 'fro');
        dual_res = rho * norm(Z - Z_old, 'fro');
        if primal_res < tol && dual_res < tol
            break;
        end
    end
end

% -------------------------------------------------------------------------
% 函数: compute_gradients
% 功能: 计算运动参数的梯度 (修正版)
% -------------------------------------------------------------------------
function [grad_alpha, grad_beta] = compute_gradients(s, x, alpha, beta, tm, mu)
    [R_num, ~] = size(s);
    grad_alpha = zeros(R_num, 1);
    grad_beta = zeros(R_num, 1);
    
    tm2 = tm.^2;
    tm3 = tm.^3;
    
    phase_mat = exp(1j * (2*pi) * (0.5 * alpha * tm2 + (1/6) * beta * tm3));
    f = x .* phase_mat;
    residual = s - f;
    
    % 计算数据保真项的梯度
    for r = 1:R_num
        % grad = -2 * Re[ <df/da, residual> ]
        % df/da = f .* (j*2*pi*0.5*tm2)
        % conj(df/da) = conj(f) .* (-j*2*pi*0.5*tm2)
        % <df/da, residual> = sum(conj(df/da) .* residual)
        % grad = -2*Re[sum(conj(f) .* (-j*pi*tm2) .* residual)]
        %      = -2*pi*sum(tm2 .* Im(conj(f) .* residual))
        grad_alpha(r) = -2 * pi * sum(tm2(r,:) .* imag(conj(f(r,:)) .* residual(r,:)));
        grad_beta(r) = -2 * pi * sum((1/3)*tm3(r,:) .* imag(conj(f(r,:)) .* residual(r,:)));
    end
    
    % 加上平滑正则项的梯度
    D = -2*eye(R_num) + diag(ones(R_num-1,1),1) + diag(ones(R_num-1,1),-1);
    D(1,1) = -1; D(end,end) = -1; % 边界处理
    
    grad_alpha = grad_alpha + mu * (D * alpha);
    grad_beta = grad_beta + mu * (D * beta);
end
