function [ISAR_image, motion_params, quality_metrics, convergence_info] = Advanced_ANM_DCFT_ISAR(s_r_tm2, tm, params)
%% 高级原子范数最小化DCFT ISAR成像算法
%
% 实现完整的SDP松弛和ADMM优化框架
%
% 输入参数:
%   s_r_tm2 - 距离压缩后的回波数据 [Nr x Nt]
%   tm - 时间向量
%   params - 算法参数结构体
%
% 输出参数:
%   ISAR_image - 成像结果
%   motion_params - 估计的运动参数
%   quality_metrics - 图像质量指标
%   convergence_info - 收敛信息

fprintf('\n========== 高级ANM-DCFT ISAR成像算法 ==========\n');

%% 1. 参数初始化
if nargin < 3
    params = struct();
end

% 默认参数设置
default_params = struct(...
    'lambda', 0.1, ...           % 稀疏正则化参数
    'mu', 0.01, ...              % 平滑正则化参数
    'rho', 1.0, ...              % ADMM惩罚参数
    'max_iter_outer', 20, ...    % 外层迭代次数
    'max_iter_inner', 30, ...    % 内层ADMM迭代次数
    'tol_outer', 1e-3, ...       % 外层收敛容差
    'tol_inner', 1e-4, ...       % 内层收敛容差
    'L_windows', 8, ...          % 频率窗口数量
    'alpha_range', [-160, 340], ... % 二次相位参数范围
    'beta_range', [-500, 2400], ... % 三次相位参数范围
    'alpha_step', 16, ...        % 二次相位搜索步长
    'beta_step', 200, ...        % 三次相位搜索步长
    'range_cells', 30:56, ...    % 处理的距离单元
    'use_tv_regularization', true, ... % 是否使用TV正则化
    'tv_weight', 0.05 ...        % TV正则化权重
);

% 合并参数
param_names = fieldnames(default_params);
for i = 1:length(param_names)
    if ~isfield(params, param_names{i})
        params.(param_names{i}) = default_params.(param_names{i});
    end
end

%% 2. 数据预处理
[Nr, Nt] = size(s_r_tm2);
R = length(params.range_cells);
M = length(tm);

% 初始化结果矩阵
ISAR_image = zeros(Nr, Nt);
motion_params = struct('alpha', zeros(R, 1), 'beta', zeros(R, 1));

% 收敛信息
convergence_info = struct('cost_outer', [], 'cost_inner', [], 'iterations_outer', 0);

fprintf('数据维度: %d x %d\n', Nr, Nt);
fprintf('处理距离单元: %d个\n', R);

%% 3. 构建频率窗口和原子字典
fprintf('构建原子字典和频率窗口...\n');

% 频率范围和窗口
F_max = 0.5; % 归一化频率范围
freq_centers = linspace(-F_max, F_max, params.L_windows);
delta_f = 2*F_max / params.L_windows;

% 时间网格
t_grid = tm(:);

%% 4. 交替优化主循环
fprintf('开始高级ANM-DCFT处理...\n');
tic;

% 初始化运动参数
alpha_vec = zeros(R, 1);
beta_vec = zeros(R, 1);

% 外层交替优化
cost_outer_history = [];

for iter_outer = 1:params.max_iter_outer
    fprintf('外层迭代 %d/%d\n', iter_outer, params.max_iter_outer);

    cost_outer_current = 0;

    %% Step 1: 固定运动参数，优化图像
    fprintf('  Step 1: 优化图像重构...\n');

    for r_idx = 1:R
        r = params.range_cells(r_idx);
        s_r = s_r_tm2(r, :).';

        % 构建当前运动参数下的观测矩阵
        A_r = construct_observation_matrix_advanced(t_grid, alpha_vec(r_idx), beta_vec(r_idx));

        % 求解原子范数最小化问题
        [x_r, cost_inner] = solve_atomic_norm_SDP_ADMM(s_r, A_r, freq_centers, delta_f, params);

        % 存储结果
        ISAR_image(r, :) = abs(x_r).';
        cost_outer_current = cost_outer_current + cost_inner;
    end

    %% Step 2: 固定图像，优化运动参数
    fprintf('  Step 2: 优化运动参数...\n');

    [alpha_vec, beta_vec] = optimize_motion_parameters(s_r_tm2, ISAR_image, t_grid, params);

    % 更新运动参数
    motion_params.alpha = alpha_vec;
    motion_params.beta = beta_vec;

    % 记录代价函数
    cost_outer_history = [cost_outer_history; cost_outer_current];

    % 检查收敛
    if iter_outer > 1 && abs(cost_outer_history(end) - cost_outer_history(end-1)) < params.tol_outer
        fprintf('  外层优化收敛于第 %d 次迭代\n', iter_outer);
        break;
    end
end

process_time = toc;
fprintf('高级ANM-DCFT处理完成，耗时: %.2f 秒\n', process_time);

%% 5. 后处理和质量评估
fprintf('计算图像质量指标...\n');

% 归一化处理
ISAR_image_norm = ISAR_image / max(ISAR_image(:));

% 计算质量指标
contrast_value = calculate_contrast_advanced(ISAR_image_norm);
entropy_value = calculate_entropy_advanced(ISAR_image_norm);
focus_measure = calculate_focus_measure_advanced(ISAR_image_norm);
sidelobe_level = calculate_sidelobe_suppression(ISAR_image_norm);

quality_metrics = struct(...
    'contrast', contrast_value, ...
    'entropy', entropy_value, ...
    'focus_measure', focus_measure, ...
    'sidelobe_level', sidelobe_level, ...
    'process_time', process_time ...
);

% 收敛信息
convergence_info.cost_outer = cost_outer_history;
convergence_info.iterations_outer = length(cost_outer_history);

%% 6. 显示结果
display_results_advanced(ISAR_image, motion_params, quality_metrics, convergence_info, params);

end

%% ==================== 核心算法函数 ====================

function A = construct_observation_matrix_advanced(t_grid, alpha, beta)
%% 构建高级观测矩阵

M = length(t_grid);

% 高阶相位项
phase_quadratic = 0.5 * alpha * t_grid.^2;
phase_cubic = (1/6) * beta * t_grid.^3;
total_phase = phase_quadratic + phase_cubic;

% 观测矩阵（对角矩阵）
A = diag(exp(-1j * 2 * pi * total_phase));

end

function [x_opt, cost_final] = solve_atomic_norm_SDP_ADMM(s, A, freq_centers, delta_f, params)
%% 使用SDP松弛和ADMM求解原子范数最小化

M = length(s);
L = length(freq_centers);

% ADMM参数
rho = params.rho;
max_iter = params.max_iter_inner;
tol = params.tol_inner;

% 初始化变量
x = zeros(M, 1);
z = zeros(M, 1);
u = zeros(M, 1);

% 预计算
AtA = A' * A;
Ats = A' * s;

% ADMM迭代
cost_history = [];

for iter = 1:max_iter
    % x-update (二次规划)
    x = (AtA + rho * eye(M)) \ (Ats + rho * (z - u));

    % z-update (原子范数的近似 - 使用频域稀疏性)
    z_temp = x + u;
    z = solve_atomic_norm_proximal(z_temp, params.lambda/rho, freq_centers, delta_f);

    % u-update (对偶变量更新)
    u = u + x - z;

    % 计算代价函数
    cost_current = 0.5 * norm(A*x - s)^2 + params.lambda * calculate_atomic_norm_approximation(x, freq_centers);
    cost_history = [cost_history; cost_current];

    % 检查收敛
    if iter > 1 && abs(cost_history(end) - cost_history(end-1)) < tol
        break;
    end
end

x_opt = x;
cost_final = cost_history(end);

end

function z = solve_atomic_norm_proximal(x, lambda, freq_centers, delta_f)
%% 求解原子范数的近端算子

% 使用频域稀疏性作为原子范数的近似
X_fft = fft(x);

% 软阈值操作
Z_fft = soft_threshold_complex(X_fft, lambda);

% 逆变换
z = ifft(Z_fft);

end

function y = soft_threshold_complex(x, threshold)
%% 复数软阈值函数

magnitude = abs(x);
phase = angle(x);

% 软阈值操作
magnitude_thresholded = max(magnitude - threshold, 0);

% 重构复数
y = magnitude_thresholded .* exp(1j * phase);

end

function norm_approx = calculate_atomic_norm_approximation(x, freq_centers)
%% 计算原子范数的近似值

% 使用频域L1范数作为近似
X_fft = fft(x);
norm_approx = sum(abs(X_fft));

end

function [alpha_opt, beta_opt] = optimize_motion_parameters(s_r_tm2, ISAR_image, t_grid, params)
%% 优化运动参数

R = length(params.range_cells);
alpha_opt = zeros(R, 1);
beta_opt = zeros(R, 1);

% 参数搜索范围
alpha_range = params.alpha_range(1):params.alpha_step:params.alpha_range(2);
beta_range = params.beta_range(1):params.beta_step:params.beta_range(2);

for r_idx = 1:R
    r = params.range_cells(r_idx);
    s_r = s_r_tm2(r, :).';
    x_r = ISAR_image(r, :).';

    best_cost = inf;

    % 网格搜索
    for alpha = alpha_range
        for beta = beta_range
            % 构建观测矩阵
            A = construct_observation_matrix_advanced(t_grid, alpha, beta);

            % 计算拟合误差
            cost = norm(s_r - A * x_r)^2;

            % 添加平滑性约束
            if r_idx > 1
                cost = cost + params.mu * ((alpha - alpha_opt(r_idx-1))^2 + (beta - beta_opt(r_idx-1))^2);
            end

            if cost < best_cost
                best_cost = cost;
                alpha_opt(r_idx) = alpha;
                beta_opt(r_idx) = beta;
            end
        end
    end
end

end

%% ==================== 质量评估函数 ====================

function contrast = calculate_contrast_advanced(image)
%% 高级对比度计算

image_abs = abs(image);
mean_val = mean(image_abs(:));
std_val = std(image_abs(:));

if mean_val == 0
    contrast = 0;
else
    contrast = std_val / mean_val;
end

end

function entropy = calculate_entropy_advanced(image)
%% 高级熵计算

image_abs = abs(image);
image_power = image_abs.^2;
total_power = sum(image_power(:));

if total_power == 0
    entropy = 0;
    return;
end

normalized_power = image_power / total_power;
valid_idx = normalized_power > eps;

if ~any(valid_idx)
    entropy = 0;
else
    entropy = -sum(normalized_power(valid_idx) .* log2(normalized_power(valid_idx)));
end

end

function focus = calculate_focus_measure_advanced(image)
%% 高级聚焦度量计算

image_abs = abs(image);

% 使用Sobel算子计算梯度
sobel_x = [-1 0 1; -2 0 2; -1 0 1];
sobel_y = [-1 -2 -1; 0 0 0; 1 2 1];

Gx = conv2(image_abs, sobel_x, 'same');
Gy = conv2(image_abs, sobel_y, 'same');

% 梯度幅度
gradient_magnitude = sqrt(Gx.^2 + Gy.^2);

% 聚焦度量
focus = var(gradient_magnitude(:));

end

function sidelobe_level = calculate_sidelobe_suppression(image)
%% 计算旁瓣抑制水平

image_abs = abs(image);

% 找到主瓣位置
[max_val, max_idx] = max(image_abs(:));
[max_row, max_col] = ind2sub(size(image_abs), max_idx);

% 定义主瓣区域（3x3邻域）
main_lobe_region = false(size(image_abs));
row_range = max(1, max_row-1):min(size(image_abs,1), max_row+1);
col_range = max(1, max_col-1):min(size(image_abs,2), max_col+1);
main_lobe_region(row_range, col_range) = true;

% 计算旁瓣能量
sidelobe_energy = sum(image_abs(~main_lobe_region).^2);
total_energy = sum(image_abs(:).^2);

% 旁瓣抑制水平（dB）
if sidelobe_energy > 0
    sidelobe_level = 10 * log10(sidelobe_energy / total_energy);
else
    sidelobe_level = -inf;
end

end

function display_results_advanced(ISAR_image, motion_params, quality_metrics, convergence_info, params)
%% 显示高级结果

fprintf('\n========== 处理结果 ==========\n');
fprintf('图像对比度: %.4f\n', quality_metrics.contrast);
fprintf('图像熵值: %.4f\n', quality_metrics.entropy);
fprintf('聚焦度量: %.4f\n', quality_metrics.focus_measure);
fprintf('旁瓣抑制: %.2f dB\n', quality_metrics.sidelobe_level);
fprintf('处理时间: %.2f 秒\n', quality_metrics.process_time);
fprintf('外层迭代次数: %d\n', convergence_info.iterations_outer);

% 显示成像结果
figure('Name', '高级ANM-DCFT ISAR成像结果', 'Position', [100, 100, 1000, 700]);

% 主成像结果
subplot(2, 2, 1);
G1 = 20*log10(abs(ISAR_image)./max(abs(ISAR_image(:))));
imagesc(G1);
caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向'); ylabel('距离向');
title('高级ANM-DCFT成像结果');
colormap jet;

% 运动参数
subplot(2, 2, 2);
plot(params.range_cells, motion_params.alpha, 'b-o', 'LineWidth', 2);
hold on;
plot(params.range_cells, motion_params.beta/10, 'r-s', 'LineWidth', 2);
grid on;
xlabel('距离单元');
ylabel('参数值');
title('估计的运动参数');
legend('α (二次)', 'β/10 (三次)', 'Location', 'best');

% 收敛曲线
subplot(2, 2, 3);
semilogy(convergence_info.cost_outer, 'g-*', 'LineWidth', 2);
grid on;
xlabel('外层迭代次数');
ylabel('代价函数值');
title('收敛曲线');

% 质量指标条形图
subplot(2, 2, 4);
metric_names = {'对比度', '1/熵值', '聚焦度', '旁瓣抑制'};
metric_values = [quality_metrics.contrast, 1/quality_metrics.entropy, ...
                quality_metrics.focus_measure/1000, abs(quality_metrics.sidelobe_level)/10];
bar(metric_values);
set(gca, 'XTickLabel', metric_names);
ylabel('归一化指标值');
title('质量指标对比');
grid on;

end
