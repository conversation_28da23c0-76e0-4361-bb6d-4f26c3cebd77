%-------------------------------------------------------------------------%
%-------- 联合参数化DCFT-稀疏-TV正则化ISAR成像算法 (JP-DCFT-TV) -------%
%-------- 基于DCFT的深度融合算法，解决散焦和旁瓣问题           -------%
%--------                      创建于2024年                    -------%
%-------------------------------------------------------------------------%
function [R_D_JP_DCFT_TV, contrast_value, entropy_value, process_time] = MRADCFT_ISAR(hrrp, fs, PRF)
% JP_DCFT_TV_ISAR - 联合参数化DCFT与稀疏TV正则化ISAR成像算法
% 
% 输入参数:
%   hrrp - 高分辨距离像数据 (复数矩阵)
%   fs   - 采样率 (Hz)
%   PRF  - 脉冲重复频率 (Hz)
% 
% 输出参数:
%   R_D_JP_DCFT_TV - 成像结果
%   contrast_value - 图像对比度
%   entropy_value  - 图像熵值
%   process_time   - 处理时间

% 获取数据尺寸
[Num_r, Num_tm] = size(hrrp);

% 计算基本参数
c = 3e8;                % 光速
fc = 5.2e9;             % 载频 (可根据实际雷达参数调整)
B = fs;                 % 假设带宽等于采样率，可根据实际调整
delta_r = c/(2*B);      % 距离分辨率
tm = (0:Num_tm-1)/PRF;  % 时间轴

% 创建用于矩阵运算的常量向量
ones_r = ones(1, Num_r);
ones_tm = ones(1, Num_tm);

% 显示距离-多普勒图像
disp('生成初始距离-多普勒图像...');
R_D = abs((fft(hrrp, Num_tm, 2)));
R_D_max = max(max(R_D));
R_D = R_D_max - R_D;

figure;
imagesc(flipud(R_D./R_D_max));
title('初始距离-多普勒图像');
xlabel('多普勒单元');
ylabel('距离单元');
colorbar;

% 选择需要处理的距离单元范围
disp('分析数据特性...');
range_energy = sum(abs(hrrp).^2, 2);
range_energy_norm = range_energy / max(range_energy);
energy_threshold = 0.05;  % 能量阈值
valid_ranges = find(range_energy_norm > energy_threshold);

if isempty(valid_ranges)
    % 如果自动检测失败，选择中间部分范围
    start_range = max(1, round(Num_r/3));
    end_range = min(Num_r, round(2*Num_r/3));
    disp('警告: 未检测到有效距离单元，使用默认范围');
else
    % 扩展有效范围，确保包含足够多的距离单元
    valid_range_center = (min(valid_ranges) + max(valid_ranges)) / 2;
    valid_range_width = max(valid_ranges) - min(valid_ranges);
    
    % 根据能量分布设定范围
    if valid_range_width < 20
        % 如果检测到的范围较窄，适当扩展
        start_range = max(1, round(valid_range_center - 15));
        end_range = min(Num_r, round(valid_range_center + 15));
    else
        % 如果检测到的范围较宽，直接使用
        start_range = max(1, min(valid_ranges) - 2);
        end_range = min(Num_r, max(valid_ranges) + 2);
    end
    
    disp(['检测到有效距离单元范围: ', num2str(start_range), ' 到 ', num2str(end_range)]);
end

% 算法参数设置
lambda1 = 0.05;          % L1正则化参数初始值
lambda2 = 0.1;           % TV正则化参数初始值
rho = 1.0;               % ADMM惩罚参数
max_iter = 30;           % 最大迭代次数
tol = 1e-4;              % 收敛容差

% 设置DCFT参数搜索范围 - 与原始DCFT保持一致
delta_alpha = 8;  % 默认二阶参数步长
delta_beta = 100; % 默认三阶参数步长

% 初始化结果矩阵
R_D_JP_DCFT_TV = zeros(Num_r, Num_tm);

% 开始处理
disp('开始JP-DCFT-TV联合优化处理...');
tic

% 处理每个距离单元
for n_r = start_range:end_range
    % 提取当前距离单元数据
    y = hrrp(n_r, :).';
    
    % DCFT参数搜索范围设置
    Min_alpha_sea = -2*delta_alpha;
    Max_alpha_sea = 40*delta_alpha;
    Min_beta_sea = -5*delta_beta;
    Max_beta_sea = 24*delta_beta;
    
    % 相位参数矩阵 - 参数化字典的核心
    alpha_grid = Min_alpha_sea:(delta_alpha*2):Max_alpha_sea;
    beta_grid = Min_beta_sea:(delta_beta*2):Max_beta_sea;
    
    % 创建参数矩阵 - 每组参数对应一个相位补偿函数
    [Alpha, Beta] = meshgrid(alpha_grid, beta_grid);
    num_params = numel(Alpha);
    
    % 初始化变量
    x = fft(y);                   % 频域初始估计
    z = x;                        % 辅助变量
    u = zeros(size(x));           % 对偶变量
    
    % 迭代优化 - 核心ADMM过程
    for iter = 1:max_iter
        % 保存上一迭代结果
        x_prev = x;
        
        % 1. 更新参数化字典和相位补偿 (DCFT部分)
        max_energy = 0;
        best_idx = 1;
        
        % 计算所有参数下的能量，找到最佳匹配
        for i = 1:num_params
            alpha_i = Alpha(i);
            beta_i = Beta(i);
            
            % 相位补偿函数
            phase_comp = exp(-1j*2*pi*((1/2)*alpha_i*tm.^2 + (1/6)*beta_i*tm.^3));
            
            % 计算与当前稀疏系数的匹配度 (深度融合核心)
            s_demod = y .* phase_comp; 
            S_demod = fft(s_demod);
            
            % 基于当前稀疏解x计算匹配能量 
            match_energy = abs(S_demod' * x).^2 / (norm(S_demod)^2 * norm(x)^2 + 1e-10);
            
            if match_energy > max_energy
                max_energy = match_energy;
                best_idx = i;
            end
        end
        
        % 获取最佳参数
        best_alpha = Alpha(best_idx);
        best_beta = Beta(best_idx);
        
        % 使用最佳参数补偿相位
        phase_comp = exp(-1j*2*pi*((1/2)*best_alpha*tm.^2 + (1/6)*best_beta*tm.^3));
        s_demod = y .* phase_comp;
        S_demod = fft(s_demod);
        
        % 2. 更新稀疏系数 (L1范数正则化部分)
        v = S_demod + rho * (z - u);
        x = v / (1 + rho);  % 二次问题解析解
        
        % 3. 更新辅助变量z (TV正则化部分)
        % 简化的TV梯度计算，不使用大型稀疏矩阵
        x = x(:); % 保证为列向量，避免拼接维度不一致
        z = z(:);
        u = u(:);
        diff_x = diff([x; x(1)]);  % 一维差分作为梯度近似，使用循环边界
        grad_weight = 1 / (1 + lambda2 * sum(abs(diff_x)));
        z = soft_threshold(x + u, lambda1/(rho*grad_weight));
        
        % 4. 更新对偶变量
        u = u + (x - z);
        
        % 收敛检查
        rel_change = norm(x - x_prev) / (norm(x_prev) + 1e-10);
        if rel_change < tol
            break;
        end
        
        % 自适应调整正则化参数
        if iter > 5
            % 根据信号稀疏度自适应调整
            lambda1 = lambda1 * 0.95;
            lambda1 = max(0.01, min(0.1, lambda1));
        end
    end
    
    % 保存结果
    R_D_JP_DCFT_TV(n_r, :) = abs(x);
    
    % 显示处理进度
    if mod(n_r - start_range + 1, 5) == 0
        disp(['已处理 ', num2str(n_r - start_range + 1), '/', ...
               num2str(end_range - start_range + 1), ' 距离单元...']);
    end
end

process_time = toc;
disp(['JP-DCFT-TV处理完成，耗时: ', num2str(process_time), ' 秒']);

% 后处理增强
disp('执行图像增强...');

% 中值滤波去除孤立点噪声
R_D_JP_DCFT_TV_filtered = medfilt2(R_D_JP_DCFT_TV, [3, 3]);

% 对比度增强
max_val = max(R_D_JP_DCFT_TV_filtered(:));
min_val = min(R_D_JP_DCFT_TV_filtered(:));
R_D_JP_DCFT_TV_enhanced = (R_D_JP_DCFT_TV_filtered - min_val) / (max_val - min_val + eps);
R_D_JP_DCFT_TV_enhanced = R_D_JP_DCFT_TV_enhanced.^0.7; % gamma校正

% 结果更新
R_D_JP_DCFT_TV = R_D_JP_DCFT_TV_enhanced;

% 显示JP-DCFT-TV成像结果
figure('name', 'JP-DCFT-TV成像结果');
G1 = 20*log10(abs(R_D_JP_DCFT_TV)./max(abs(R_D_JP_DCFT_TV(:)) + eps));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向 (m)');
colormap jet;
title('JP-DCFT-TV ISAR成像结果');

% 计算对比度和熵值
contrast_value = contrast(R_D_JP_DCFT_TV+eps);
entropy_value = EntropyImage(R_D_JP_DCFT_TV+eps);

disp(['图像对比度: ', num2str(contrast_value)]);
disp(['图像熵值: ', num2str(entropy_value)]);

% 保存结果
save('JP_DCFT_TV_ISAR_Results.mat', 'R_D_JP_DCFT_TV', 'contrast_value', 'entropy_value', 'process_time');
disp('结果已保存至 JP_DCFT_TV_ISAR_Results.mat');

end

%% 辅助函数

% 软阈值函数
function y = soft_threshold(x, thresh)
    y = sign(x) .* max(abs(x) - thresh, 0);
end

% 对比度计算函数
function C = contrast(image)
    image = abs(image);
    u = mean(image(:));
    sigma = std(image(:));
    C = sigma/u;
    disp(['对比度: ', num2str(C)]);
end

% 熵值计算函数
function E = EntropyImage(image)
    image = abs(image);
    image = image/sum(image(:));
    idx = find(image>0);
    E = -sum(image(idx).*log2(image(idx)));
    disp(['熵值: ', num2str(E)]);
end