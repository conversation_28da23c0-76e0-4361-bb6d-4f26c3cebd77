function [R_D_ANM_DCFT, quality_metrics, motion_params] = ANM_DCFT_ISAR_Fixed(s_r_tm2, tm, params)
%% 基于原子范数最小化的DCFT ISAR成像算法 - 修正版本
% 
% 完全基于原始DCFT框架，集成原子范数最小化思想
%
% 输入参数:
%   s_r_tm2 - 距离压缩后的回波数据 [Nr x Nt]
%   tm - 时间向量
%   params - 算法参数结构体
%
% 输出参数:
%   R_D_ANM_DCFT - ANM-DCFT成像结果
%   quality_metrics - 图像质量指标
%   motion_params - 估计的运动参数

fprintf('\n========== ANM-DCFT ISAR成像算法 (修正版) ==========\n');

%% 1. 参数初始化
if nargin < 3
    params = struct();
end

% 默认参数设置（与原始DCFT保持一致）
default_params = struct(...
    'lambda', 0.1, ...           % ANM正则化参数
    'range_cells', 30:56, ...    % 处理的距离单元
    'delta_alpha', 8, ...        % 二次相位搜索步长
    'alpha_min', -16, ...        % 二次相位最小值
    'alpha_max', 320, ...        % 二次相位最大值
    'delta_beta', 100, ...       % 三次相位搜索步长
    'beta_min', -500, ...        % 三次相位最小值
    'beta_max', 2400, ...        % 三次相位最大值
    'FactorDop', 1 ...           % Doppler插值因子
);

% 合并参数
param_names = fieldnames(default_params);
for i = 1:length(param_names)
    if ~isfield(params, param_names{i})
        params.(param_names{i}) = default_params.(param_names{i});
    end
end

%% 2. 数据预处理
[Num_r, Num_tm] = size(s_r_tm2);
FactorDop = params.FactorDop;

% 初始化结果矩阵
R_D_ANM_DCFT = zeros(Num_r, FactorDop*Num_tm);

% 运动参数存储
motion_params = struct();
alpha_estimated = [];
beta_estimated = [];

fprintf('数据维度: %d x %d\n', Num_r, Num_tm);
fprintf('处理距离单元: %d - %d\n', params.range_cells(1), params.range_cells(end));

%% 3. 参数搜索范围设置
alpha_sea = params.alpha_min:params.delta_alpha:params.alpha_max;
beta_sea = params.beta_min:params.delta_beta:params.beta_max;
Num_alpha_sea = length(alpha_sea);
Num_beta_sea = length(beta_sea);

fprintf('参数搜索范围: α[%d:%d:%d], β[%d:%d:%d]\n', ...
    params.alpha_min, params.delta_alpha, params.alpha_max, ...
    params.beta_min, params.delta_beta, params.beta_max);

%% 4. ANM-DCFT主处理循环
fprintf('开始ANM-DCFT处理...\n');
tic;

for n_delta_r = params.range_cells
    if mod(n_delta_r - params.range_cells(1), 5) == 0
        fprintf('处理距离单元 %d\n', n_delta_r);
    end
    
    % 当前距离单元的信号
    s_tm_temp = s_r_tm2(n_delta_r, :);
    
    %% Step 1: 寻找最大值阈值（与原始DCFT相同）
    ISAR_f_a_b_max = 0;
    for n_alpha = 1:Num_alpha_sea
        for n_beta = 1:Num_beta_sea
            % DCFT相位补偿
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beta_sea(n_beta)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            
            % FFT变换
            S_dechirp = fft(s_dechirp);
            
            % 更新最大值
            if max(abs(S_dechirp)) > ISAR_f_a_b_max
                ISAR_f_a_b_max = max(abs(S_dechirp));
            end
        end
    end
    
    %% Step 2: ANM增强的Doppler分布计算
    S_f = zeros(1, FactorDop*Num_tm);
    best_alpha = 0;
    best_beta = 0;
    best_quality = 0;
    
    for n_alpha = 1:Num_alpha_sea
        for n_beta = 1:Num_beta_sea
            % DCFT相位补偿
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm.^2 + (1/6)*beta_sea(n_beta)*tm.^3));
            s_dechirp = s_tm_temp .* s_comp;
            
            % FFT变换
            S_dechirp = fft(s_dechirp, FactorDop*Num_tm);
            
            % ANM增强处理
            S_enhanced = apply_ANM_enhancement(S_dechirp, params.lambda, ISAR_f_a_b_max);
            
            % 计算质量指标用于参数选择
            quality_current = calculate_spectrum_quality(S_enhanced);
            
            % 更新最佳参数
            if quality_current > best_quality
                best_quality = quality_current;
                best_alpha = alpha_sea(n_alpha);
                best_beta = beta_sea(n_beta);
            end
            
            % 频谱累积（按照原始DCFT逻辑）
            for n_tm = 1:FactorDop*Num_tm
                if abs(S_enhanced(n_tm)) > (ISAR_f_a_b_max * 0.0)
                    S_f(n_tm) = S_f(n_tm) + S_enhanced(n_tm);
                end
            end
        end
    end
    
    % 存储结果
    R_D_ANM_DCFT(n_delta_r, :) = abs(S_f);
    
    % 记录运动参数
    alpha_estimated = [alpha_estimated; best_alpha];
    beta_estimated = [beta_estimated; best_beta];
end

process_time = toc;
fprintf('ANM-DCFT处理完成，耗时: %.2f 秒\n', process_time);

%% 5. 后处理和质量评估
fprintf('计算图像质量指标...\n');

% 存储运动参数
motion_params.alpha = alpha_estimated;
motion_params.beta = beta_estimated;
motion_params.range_cells = params.range_cells;

% 计算质量指标
contrast_value = calculate_contrast_metric(R_D_ANM_DCFT);
entropy_value = calculate_entropy_metric(R_D_ANM_DCFT);
focus_measure = calculate_focus_metric(R_D_ANM_DCFT);

quality_metrics = struct(...
    'contrast', contrast_value, ...
    'entropy', entropy_value, ...
    'focus_measure', focus_measure, ...
    'process_time', process_time ...
);

%% 6. 显示结果
fprintf('\n========== 处理结果 ==========\n');
fprintf('图像对比度: %.4f\n', contrast_value);
fprintf('图像熵值: %.4f\n', entropy_value);
fprintf('聚焦度量: %.4f\n', focus_measure);
fprintf('处理时间: %.2f 秒\n', process_time);

% 显示成像结果
figure('Name', 'ANM-DCFT ISAR成像结果 (修正版)', 'Position', [100, 100, 800, 600]);
G1 = 20*log10(abs(R_D_ANM_DCFT)./max(abs(R_D_ANM_DCFT(:))));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向');
colormap jet;
title('ANM-DCFT ISAR成像结果 (修正版)');

% 显示运动参数
if ~isempty(alpha_estimated)
    figure('Name', '估计的运动参数', 'Position', [900, 100, 600, 400]);
    subplot(2,1,1);
    plot(params.range_cells, alpha_estimated, 'b-o', 'LineWidth', 2);
    grid on;
    xlabel('距离单元');
    ylabel('α (二次相位参数)');
    title('估计的二次相位参数');
    
    subplot(2,1,2);
    plot(params.range_cells, beta_estimated, 'r-s', 'LineWidth', 2);
    grid on;
    xlabel('距离单元');
    ylabel('β (三次相位参数)');
    title('估计的三次相位参数');
end

end

%% ==================== 核心ANM增强函数 ====================

function S_enhanced = apply_ANM_enhancement(S_fft, lambda, max_threshold)
%% 应用原子范数最小化增强

% 计算频谱幅度和相位
magnitude = abs(S_fft);
phase = angle(S_fft);

% ANM软阈值处理
threshold = lambda * max_threshold * 0.05; % 自适应阈值
magnitude_enhanced = soft_threshold_anm(magnitude, threshold);

% 旁瓣抑制：进一步抑制弱信号
sidelobe_threshold = 0.2 * max(magnitude_enhanced);
magnitude_enhanced(magnitude_enhanced < sidelobe_threshold) = ...
    magnitude_enhanced(magnitude_enhanced < sidelobe_threshold) * 0.5;

% 重构增强的频谱
S_enhanced = magnitude_enhanced .* exp(1j * phase);

end

function y = soft_threshold_anm(x, threshold)
%% ANM软阈值函数

y = sign(x) .* max(abs(x) - threshold, 0);

end

function quality = calculate_spectrum_quality(S_fft)
%% 计算频谱质量指标

magnitude = abs(S_fft);

% 主瓣能量集中度
max_val = max(magnitude);
main_lobe_energy = sum(magnitude(magnitude > 0.5*max_val).^2);
total_energy = sum(magnitude.^2);

% 质量指标：主瓣集中度
if total_energy > 0
    quality = main_lobe_energy / total_energy;
else
    quality = 0;
end

end

%% ==================== 质量评估函数 ====================

function contrast = calculate_contrast_metric(image)
%% 计算图像对比度

image_abs = abs(image);
mean_val = mean(image_abs(:));
std_val = std(image_abs(:));

if mean_val == 0
    contrast = 0;
else
    contrast = std_val / mean_val;
end

end

function entropy = calculate_entropy_metric(image)
%% 计算图像熵

image_abs = abs(image);
image_norm = image_abs / sum(image_abs(:));
valid_idx = image_norm > eps;

if ~any(valid_idx)
    entropy = 0;
else
    entropy = -sum(image_norm(valid_idx) .* log2(image_norm(valid_idx)));
end

end

function focus = calculate_focus_metric(image)
%% 计算聚焦度量

image_abs = abs(image);
[Gx, Gy] = gradient(image_abs);
focus = var(sqrt(Gx.^2 + Gy.^2), [], 'all');

end
